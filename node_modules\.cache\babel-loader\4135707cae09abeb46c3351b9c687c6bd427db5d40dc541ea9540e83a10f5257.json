{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ParkingMeter = createLucideIcon(\"ParkingMeter\", [[\"path\", {\n  d: \"M9 9a3 3 0 1 1 6 0\",\n  key: \"jdoeu8\"\n}], [\"path\", {\n  d: \"M12 12v3\",\n  key: \"158kv8\"\n}], [\"path\", {\n  d: \"M11 15h2\",\n  key: \"199qp6\"\n}], [\"path\", {\n  d: \"M19 9a7 7 0 1 0-13.6 2.3C6.4 14.4 8 19 8 19h8s1.6-4.6 2.6-7.7c.3-.8.4-1.5.4-2.3\",\n  key: \"1l50wn\"\n}], [\"path\", {\n  d: \"M12 19v3\",\n  key: \"npa21l\"\n}]]);\nexport { ParkingMeter as default };", "map": {"version": 3, "names": ["ParkingMeter", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\parking-meter.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ParkingMeter\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSA5YTMgMyAwIDEgMSA2IDAiIC8+CiAgPHBhdGggZD0iTTEyIDEydjMiIC8+CiAgPHBhdGggZD0iTTExIDE1aDIiIC8+CiAgPHBhdGggZD0iTTE5IDlhNyA3IDAgMSAwLTEzLjYgMi4zQzYuNCAxNC40IDggMTkgOCAxOWg4czEuNi00LjYgMi42LTcuN2MuMy0uOC40LTEuNS40LTIuMyIgLz4KICA8cGF0aCBkPSJNMTIgMTl2MyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/parking-meter\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ParkingMeter = createLucideIcon('ParkingMeter', [\n  ['path', { d: 'M9 9a3 3 0 1 1 6 0', key: 'jdoeu8' }],\n  ['path', { d: 'M12 12v3', key: '158kv8' }],\n  ['path', { d: 'M11 15h2', key: '199qp6' }],\n  [\n    'path',\n    {\n      d: 'M19 9a7 7 0 1 0-13.6 2.3C6.4 14.4 8 19 8 19h8s1.6-4.6 2.6-7.7c.3-.8.4-1.5.4-2.3',\n      key: '1l50wn',\n    },\n  ],\n  ['path', { d: 'M12 19v3', key: 'npa21l' }],\n]);\n\nexport default ParkingMeter;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}