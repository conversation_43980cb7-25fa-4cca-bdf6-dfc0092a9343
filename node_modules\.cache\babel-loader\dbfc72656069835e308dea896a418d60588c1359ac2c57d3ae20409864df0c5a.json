{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\nclass ApiService {\n  constructor() {\n    this.token = localStorage.getItem('token');\n  }\n  setToken(token) {\n    this.token = token;\n    if (token) {\n      localStorage.setItem('token', token);\n    } else {\n      localStorage.removeItem('token');\n    }\n  }\n  async request(endpoint, options = {}) {\n    const url = `${API_BASE_URL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    };\n    if (this.token) {\n      config.headers.Authorization = `Bearer ${this.token}`;\n    }\n    if (config.body && typeof config.body !== 'string') {\n      config.body = JSON.stringify(config.body);\n    }\n    try {\n      const response = await fetch(url, config);\n      if (response.status === 401) {\n        this.setToken(null);\n        window.location.href = '/login';\n        return;\n      }\n      if (!response.ok) {\n        let errorMessage = `HTTP error! status: ${response.status}`;\n        try {\n          const errorData = await response.json();\n          if (errorData.detail) {\n            errorMessage = errorData.detail;\n          } else if (errorData.message) {\n            errorMessage = errorData.message;\n          }\n        } catch (e) {\n          // If we can't parse the error response, use the status message\n          errorMessage = `Server error: ${response.status} ${response.statusText}`;\n        }\n        throw new Error(errorMessage);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('API request failed:', error);\n      throw error;\n    }\n  }\n\n  // Auth endpoints\n  async login(username, password) {\n    const formData = new FormData();\n    formData.append('username', username);\n    formData.append('password', password);\n    return this.request('/auth/token', {\n      method: 'POST',\n      headers: {},\n      body: formData\n    });\n  }\n  async getCurrentUser() {\n    return this.request('/auth/me');\n  }\n  async register(userData) {\n    return this.request('/auth/register', {\n      method: 'POST',\n      body: userData\n    });\n  }\n\n  // Partner endpoints\n  async getPartners() {\n    return this.request('/partners');\n  }\n  async createPartner(partnerData) {\n    return this.request('/partners', {\n      method: 'POST',\n      body: partnerData\n    });\n  }\n  async updatePartner(partnerId, partnerData) {\n    return this.request(`/partners/${partnerId}`, {\n      method: 'PUT',\n      body: partnerData\n    });\n  }\n  async getPartner(partnerId) {\n    return this.request(`/partners/${partnerId}`);\n  }\n  async getPartnerBalance(partnerId) {\n    return this.request(`/partners/${partnerId}/balance`);\n  }\n\n  // Transaction endpoints\n  async getTransactions(partnerId = null) {\n    const params = partnerId ? `?partner_id=${partnerId}` : '';\n    return this.request(`/transactions${params}`);\n  }\n  async createTransaction(transactionData) {\n    return this.request('/transactions', {\n      method: 'POST',\n      body: transactionData\n    });\n  }\n  async updateTransaction(transactionId, transactionData) {\n    return this.request(`/transactions/${transactionId}`, {\n      method: 'PUT',\n      body: transactionData\n    });\n  }\n  async processBalance(balanceData) {\n    return this.request('/transactions/balance', {\n      method: 'POST',\n      body: balanceData\n    });\n  }\n\n  // Reports endpoints\n  async getDashboardStats() {\n    return this.request('/reports/dashboard');\n  }\n  async getDailyReport(date = null) {\n    const params = date ? `?date=${date}` : '';\n    return this.request(`/reports/daily${params}`);\n  }\n}\nexport default new ApiService();", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiService", "constructor", "token", "localStorage", "getItem", "setToken", "setItem", "removeItem", "request", "endpoint", "options", "url", "config", "headers", "Authorization", "body", "JSON", "stringify", "response", "fetch", "status", "window", "location", "href", "ok", "errorMessage", "errorData", "json", "detail", "message", "e", "statusText", "Error", "error", "console", "login", "username", "password", "formData", "FormData", "append", "method", "getCurrentUser", "register", "userData", "getPartners", "createPartner", "partnerData", "update<PERSON><PERSON><PERSON>", "partnerId", "<PERSON><PERSON><PERSON><PERSON>", "getPartnerBalance", "getTransactions", "params", "createTransaction", "transactionData", "updateTransaction", "transactionId", "processBalance", "balanceData", "getDashboardStats", "getDailyReport", "date"], "sources": ["C:/Users/<USER>/others/bdc_app/src/services/api.js"], "sourcesContent": ["const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\nclass ApiService {\n  constructor() {\n    this.token = localStorage.getItem('token');\n  }\n\n  setToken(token) {\n    this.token = token;\n    if (token) {\n      localStorage.setItem('token', token);\n    } else {\n      localStorage.removeItem('token');\n    }\n  }\n\n  async request(endpoint, options = {}) {\n    const url = `${API_BASE_URL}${endpoint}`;\n    const config = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    if (this.token) {\n      config.headers.Authorization = `Bearer ${this.token}`;\n    }\n\n    if (config.body && typeof config.body !== 'string') {\n      config.body = JSON.stringify(config.body);\n    }\n\n    try {\n      const response = await fetch(url, config);\n      \n      if (response.status === 401) {\n        this.setToken(null);\n        window.location.href = '/login';\n        return;\n      }\n      \n      if (!response.ok) {\n        let errorMessage = `HTTP error! status: ${response.status}`;\n        try {\n          const errorData = await response.json();\n          if (errorData.detail) {\n            errorMessage = errorData.detail;\n          } else if (errorData.message) {\n            errorMessage = errorData.message;\n          }\n        } catch (e) {\n          // If we can't parse the error response, use the status message\n          errorMessage = `Server error: ${response.status} ${response.statusText}`;\n        }\n        throw new Error(errorMessage);\n      }\n      \n      return await response.json();\n    } catch (error) {\n      console.error('API request failed:', error);\n      throw error;\n    }\n  }\n\n  // Auth endpoints\n  async login(username, password) {\n    const formData = new FormData();\n    formData.append('username', username);\n    formData.append('password', password);\n    \n    return this.request('/auth/token', {\n      method: 'POST',\n      headers: {},\n      body: formData,\n    });\n  }\n\n  async getCurrentUser() {\n    return this.request('/auth/me');\n  }\n\n  async register(userData) {\n    return this.request('/auth/register', {\n      method: 'POST',\n      body: userData,\n    });\n  }\n\n  // Partner endpoints\n  async getPartners() {\n    return this.request('/partners');\n  }\n\n  async createPartner(partnerData) {\n    return this.request('/partners', {\n      method: 'POST',\n      body: partnerData,\n    });\n  }\n\n  async updatePartner(partnerId, partnerData) {\n    return this.request(`/partners/${partnerId}`, {\n      method: 'PUT',\n      body: partnerData,\n    });\n  }\n\n  async getPartner(partnerId) {\n    return this.request(`/partners/${partnerId}`);\n  }\n\n  async getPartnerBalance(partnerId) {\n    return this.request(`/partners/${partnerId}/balance`);\n  }\n\n  // Transaction endpoints\n  async getTransactions(partnerId = null) {\n    const params = partnerId ? `?partner_id=${partnerId}` : '';\n    return this.request(`/transactions${params}`);\n  }\n\n  async createTransaction(transactionData) {\n    return this.request('/transactions', {\n      method: 'POST',\n      body: transactionData,\n    });\n  }\n\n  async updateTransaction(transactionId, transactionData) {\n    return this.request(`/transactions/${transactionId}`, {\n      method: 'PUT',\n      body: transactionData,\n    });\n  }\n\n  async processBalance(balanceData) {\n    return this.request('/transactions/balance', {\n      method: 'POST',\n      body: balanceData,\n    });\n  }\n\n  // Reports endpoints\n  async getDashboardStats() {\n    return this.request('/reports/dashboard');\n  }\n\n  async getDailyReport(date = null) {\n    const params = date ? `?date=${date}` : '';\n    return this.request(`/reports/daily${params}`);\n  }\n}\n\nexport default new ApiService();\n"], "mappings": "AAAA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC5C;EAEAC,QAAQA,CAACH,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAIA,KAAK,EAAE;MACTC,YAAY,CAACG,OAAO,CAAC,OAAO,EAAEJ,KAAK,CAAC;IACtC,CAAC,MAAM;MACLC,YAAY,CAACI,UAAU,CAAC,OAAO,CAAC;IAClC;EACF;EAEA,MAAMC,OAAOA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAMC,GAAG,GAAG,GAAGf,YAAY,GAAGa,QAAQ,EAAE;IACxC,MAAMG,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,GAAGH,OAAO,CAACG;MACb,CAAC;MACD,GAAGH;IACL,CAAC;IAED,IAAI,IAAI,CAACR,KAAK,EAAE;MACdU,MAAM,CAACC,OAAO,CAACC,aAAa,GAAG,UAAU,IAAI,CAACZ,KAAK,EAAE;IACvD;IAEA,IAAIU,MAAM,CAACG,IAAI,IAAI,OAAOH,MAAM,CAACG,IAAI,KAAK,QAAQ,EAAE;MAClDH,MAAM,CAACG,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,IAAI,CAAC;IAC3C;IAEA,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACR,GAAG,EAAEC,MAAM,CAAC;MAEzC,IAAIM,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;QAC3B,IAAI,CAACf,QAAQ,CAAC,IAAI,CAAC;QACnBgB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B;MACF;MAEA,IAAI,CAACL,QAAQ,CAACM,EAAE,EAAE;QAChB,IAAIC,YAAY,GAAG,uBAAuBP,QAAQ,CAACE,MAAM,EAAE;QAC3D,IAAI;UACF,MAAMM,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;UACvC,IAAID,SAAS,CAACE,MAAM,EAAE;YACpBH,YAAY,GAAGC,SAAS,CAACE,MAAM;UACjC,CAAC,MAAM,IAAIF,SAAS,CAACG,OAAO,EAAE;YAC5BJ,YAAY,GAAGC,SAAS,CAACG,OAAO;UAClC;QACF,CAAC,CAAC,OAAOC,CAAC,EAAE;UACV;UACAL,YAAY,GAAG,iBAAiBP,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACa,UAAU,EAAE;QAC1E;QACA,MAAM,IAAIC,KAAK,CAACP,YAAY,CAAC;MAC/B;MAEA,OAAO,MAAMP,QAAQ,CAACS,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAME,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC9B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,QAAQ,CAAC;IACrCE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;IAErC,OAAO,IAAI,CAAC7B,OAAO,CAAC,aAAa,EAAE;MACjCiC,MAAM,EAAE,MAAM;MACd5B,OAAO,EAAE,CAAC,CAAC;MACXE,IAAI,EAAEuB;IACR,CAAC,CAAC;EACJ;EAEA,MAAMI,cAAcA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAClC,OAAO,CAAC,UAAU,CAAC;EACjC;EAEA,MAAMmC,QAAQA,CAACC,QAAQ,EAAE;IACvB,OAAO,IAAI,CAACpC,OAAO,CAAC,gBAAgB,EAAE;MACpCiC,MAAM,EAAE,MAAM;MACd1B,IAAI,EAAE6B;IACR,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACrC,OAAO,CAAC,WAAW,CAAC;EAClC;EAEA,MAAMsC,aAAaA,CAACC,WAAW,EAAE;IAC/B,OAAO,IAAI,CAACvC,OAAO,CAAC,WAAW,EAAE;MAC/BiC,MAAM,EAAE,MAAM;MACd1B,IAAI,EAAEgC;IACR,CAAC,CAAC;EACJ;EAEA,MAAMC,aAAaA,CAACC,SAAS,EAAEF,WAAW,EAAE;IAC1C,OAAO,IAAI,CAACvC,OAAO,CAAC,aAAayC,SAAS,EAAE,EAAE;MAC5CR,MAAM,EAAE,KAAK;MACb1B,IAAI,EAAEgC;IACR,CAAC,CAAC;EACJ;EAEA,MAAMG,UAAUA,CAACD,SAAS,EAAE;IAC1B,OAAO,IAAI,CAACzC,OAAO,CAAC,aAAayC,SAAS,EAAE,CAAC;EAC/C;EAEA,MAAME,iBAAiBA,CAACF,SAAS,EAAE;IACjC,OAAO,IAAI,CAACzC,OAAO,CAAC,aAAayC,SAAS,UAAU,CAAC;EACvD;;EAEA;EACA,MAAMG,eAAeA,CAACH,SAAS,GAAG,IAAI,EAAE;IACtC,MAAMI,MAAM,GAAGJ,SAAS,GAAG,eAAeA,SAAS,EAAE,GAAG,EAAE;IAC1D,OAAO,IAAI,CAACzC,OAAO,CAAC,gBAAgB6C,MAAM,EAAE,CAAC;EAC/C;EAEA,MAAMC,iBAAiBA,CAACC,eAAe,EAAE;IACvC,OAAO,IAAI,CAAC/C,OAAO,CAAC,eAAe,EAAE;MACnCiC,MAAM,EAAE,MAAM;MACd1B,IAAI,EAAEwC;IACR,CAAC,CAAC;EACJ;EAEA,MAAMC,iBAAiBA,CAACC,aAAa,EAAEF,eAAe,EAAE;IACtD,OAAO,IAAI,CAAC/C,OAAO,CAAC,iBAAiBiD,aAAa,EAAE,EAAE;MACpDhB,MAAM,EAAE,KAAK;MACb1B,IAAI,EAAEwC;IACR,CAAC,CAAC;EACJ;EAEA,MAAMG,cAAcA,CAACC,WAAW,EAAE;IAChC,OAAO,IAAI,CAACnD,OAAO,CAAC,uBAAuB,EAAE;MAC3CiC,MAAM,EAAE,MAAM;MACd1B,IAAI,EAAE4C;IACR,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,iBAAiBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACpD,OAAO,CAAC,oBAAoB,CAAC;EAC3C;EAEA,MAAMqD,cAAcA,CAACC,IAAI,GAAG,IAAI,EAAE;IAChC,MAAMT,MAAM,GAAGS,IAAI,GAAG,SAASA,IAAI,EAAE,GAAG,EAAE;IAC1C,OAAO,IAAI,CAACtD,OAAO,CAAC,iBAAiB6C,MAAM,EAAE,CAAC;EAChD;AACF;AAEA,eAAe,IAAIrD,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}