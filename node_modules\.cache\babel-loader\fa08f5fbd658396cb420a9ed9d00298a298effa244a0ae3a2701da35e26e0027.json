{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BookDashed = createLucideIcon(\"BookDashed\", [[\"path\", {\n  d: \"M20 22h-2\",\n  key: \"1rpnb6\"\n}], [\"path\", {\n  d: \"M20 15v2h-2\",\n  key: \"fph276\"\n}], [\"path\", {\n  d: \"M4 19.5V15\",\n  key: \"6gr39e\"\n}], [\"path\", {\n  d: \"M20 8v3\",\n  key: \"deu0bs\"\n}], [\"path\", {\n  d: \"M18 2h2v2\",\n  key: \"180o53\"\n}], [\"path\", {\n  d: \"M4 11V9\",\n  key: \"v3xsx8\"\n}], [\"path\", {\n  d: \"M12 2h2\",\n  key: \"cvn524\"\n}], [\"path\", {\n  d: \"M12 22h2\",\n  key: \"kn7ki6\"\n}], [\"path\", {\n  d: \"M12 17h2\",\n  key: \"13u4lk\"\n}], [\"path\", {\n  d: \"M8 22H6.5a2.5 2.5 0 0 1 0-5H8\",\n  key: \"fiseg2\"\n}], [\"path\", {\n  d: \"M4 5v-.5A2.5 2.5 0 0 1 6.5 2H8\",\n  key: \"wywhs9\"\n}]]);\nexport { BookDashed as default };", "map": {"version": 3, "names": ["BookDashed", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\book-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BookDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMjJoLTIiIC8+CiAgPHBhdGggZD0iTTIwIDE1djJoLTIiIC8+CiAgPHBhdGggZD0iTTQgMTkuNVYxNSIgLz4KICA8cGF0aCBkPSJNMjAgOHYzIiAvPgogIDxwYXRoIGQ9Ik0xOCAyaDJ2MiIgLz4KICA8cGF0aCBkPSJNNCAxMVY5IiAvPgogIDxwYXRoIGQ9Ik0xMiAyaDIiIC8+CiAgPHBhdGggZD0iTTEyIDIyaDIiIC8+CiAgPHBhdGggZD0iTTEyIDE3aDIiIC8+CiAgPHBhdGggZD0iTTggMjJINi41YTIuNSAyLjUgMCAwIDEgMC01SDgiIC8+CiAgPHBhdGggZD0iTTQgNXYtLjVBMi41IDIuNSAwIDAgMSA2LjUgMkg4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookDashed = createLucideIcon('BookDashed', [\n  ['path', { d: 'M20 22h-2', key: '1rpnb6' }],\n  ['path', { d: 'M20 15v2h-2', key: 'fph276' }],\n  ['path', { d: 'M4 19.5V15', key: '6gr39e' }],\n  ['path', { d: 'M20 8v3', key: 'deu0bs' }],\n  ['path', { d: 'M18 2h2v2', key: '180o53' }],\n  ['path', { d: 'M4 11V9', key: 'v3xsx8' }],\n  ['path', { d: 'M12 2h2', key: 'cvn524' }],\n  ['path', { d: 'M12 22h2', key: 'kn7ki6' }],\n  ['path', { d: 'M12 17h2', key: '13u4lk' }],\n  ['path', { d: 'M8 22H6.5a2.5 2.5 0 0 1 0-5H8', key: 'fiseg2' }],\n  ['path', { d: 'M4 5v-.5A2.5 2.5 0 0 1 6.5 2H8', key: 'wywhs9' }],\n]);\n\nexport default BookDashed;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}