{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst StepForward = createLucideIcon(\"StepForward\", [[\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"4\",\n  y2: \"20\",\n  key: \"fy8qot\"\n}], [\"polygon\", {\n  points: \"10,4 20,12 10,20\",\n  key: \"1mc1pf\"\n}]]);\nexport { StepForward as default };", "map": {"version": 3, "names": ["StepForward", "createLucideIcon", "x1", "x2", "y1", "y2", "key", "points"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\step-forward.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name StepForward\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iNiIgeDI9IjYiIHkxPSI0IiB5Mj0iMjAiIC8+CiAgPHBvbHlnb24gcG9pbnRzPSIxMCw0IDIwLDEyIDEwLDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/step-forward\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst StepForward = createLucideIcon('StepForward', [\n  ['line', { x1: '6', x2: '6', y1: '4', y2: '20', key: 'fy8qot' }],\n  ['polygon', { points: '10,4 20,12 10,20', key: '1mc1pf' }],\n]);\n\nexport default StepForward;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,SAAW;EAAEC,MAAA,EAAQ,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}