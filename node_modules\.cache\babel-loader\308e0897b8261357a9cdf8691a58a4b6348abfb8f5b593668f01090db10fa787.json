{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Layers = createLucideIcon(\"Layers\", [[\"path\", {\n  d: \"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z\",\n  key: \"8b97xw\"\n}], [\"path\", {\n  d: \"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65\",\n  key: \"dd6zsq\"\n}], [\"path\", {\n  d: \"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65\",\n  key: \"ep9fru\"\n}]]);\nexport { Layers as default };", "map": {"version": 3, "names": ["Layers", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\layers.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Layers\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIuODMgMi4xOGEyIDIgMCAwIDAtMS42NiAwTDIuNiA2LjA4YTEgMSAwIDAgMCAwIDEuODNsOC41OCAzLjkxYTIgMiAwIDAgMCAxLjY2IDBsOC41OC0zLjlhMSAxIDAgMCAwIDAtMS44M1oiIC8+CiAgPHBhdGggZD0ibTIyIDE3LjY1LTkuMTcgNC4xNmEyIDIgMCAwIDEtMS42NiAwTDIgMTcuNjUiIC8+CiAgPHBhdGggZD0ibTIyIDEyLjY1LTkuMTcgNC4xNmEyIDIgMCAwIDEtMS42NiAwTDIgMTIuNjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/layers\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Layers = createLucideIcon('Layers', [\n  [\n    'path',\n    {\n      d: 'm12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z',\n      key: '8b97xw',\n    },\n  ],\n  ['path', { d: 'm22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65', key: 'dd6zsq' }],\n  ['path', { d: 'm22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65', key: 'ep9fru' }],\n]);\n\nexport default Layers;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}