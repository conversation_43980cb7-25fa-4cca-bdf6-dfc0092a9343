{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst UserRoundCog = createLucideIcon(\"UserRoundCog\", [[\"path\", {\n  d: \"M2 21a8 8 0 0 1 10.434-7.62\",\n  key: \"1yezr2\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"8\",\n  r: \"5\",\n  key: \"o932ke\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}], [\"path\", {\n  d: \"m19.5 14.3-.4.9\",\n  key: \"1eb35c\"\n}], [\"path\", {\n  d: \"m16.9 20.8-.4.9\",\n  key: \"dfjc4z\"\n}], [\"path\", {\n  d: \"m21.7 19.5-.9-.4\",\n  key: \"q4dx6b\"\n}], [\"path\", {\n  d: \"m15.2 16.9-.9-.4\",\n  key: \"1r0w5f\"\n}], [\"path\", {\n  d: \"m21.7 16.5-.9.4\",\n  key: \"1knoei\"\n}], [\"path\", {\n  d: \"m15.2 19.1-.9.4\",\n  key: \"j188fs\"\n}], [\"path\", {\n  d: \"m19.5 21.7-.4-.9\",\n  key: \"1tonu5\"\n}], [\"path\", {\n  d: \"m16.9 15.2-.4-.9\",\n  key: \"699xu\"\n}]]);\nexport { UserRoundCog as default };", "map": {"version": 3, "names": ["UserRoundCog", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\user-round-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserRoundCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMWE4IDggMCAwIDEgMTAuNDM0LTcuNjIiIC8+CiAgPGNpcmNsZSBjeD0iMTAiIGN5PSI4IiByPSI1IiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMTgiIHI9IjMiIC8+CiAgPHBhdGggZD0ibTE5LjUgMTQuMy0uNC45IiAvPgogIDxwYXRoIGQ9Im0xNi45IDIwLjgtLjQuOSIgLz4KICA8cGF0aCBkPSJtMjEuNyAxOS41LS45LS40IiAvPgogIDxwYXRoIGQ9Im0xNS4yIDE2LjktLjktLjQiIC8+CiAgPHBhdGggZD0ibTIxLjcgMTYuNS0uOS40IiAvPgogIDxwYXRoIGQ9Im0xNS4yIDE5LjEtLjkuNCIgLz4KICA8cGF0aCBkPSJtMTkuNSAyMS43LS40LS45IiAvPgogIDxwYXRoIGQ9Im0xNi45IDE1LjItLjQtLjkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user-round-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserRoundCog = createLucideIcon('UserRoundCog', [\n  ['path', { d: 'M2 21a8 8 0 0 1 10.434-7.62', key: '1yezr2' }],\n  ['circle', { cx: '10', cy: '8', r: '5', key: 'o932ke' }],\n  ['circle', { cx: '18', cy: '18', r: '3', key: '1xkwt0' }],\n  ['path', { d: 'm19.5 14.3-.4.9', key: '1eb35c' }],\n  ['path', { d: 'm16.9 20.8-.4.9', key: 'dfjc4z' }],\n  ['path', { d: 'm21.7 19.5-.9-.4', key: 'q4dx6b' }],\n  ['path', { d: 'm15.2 16.9-.9-.4', key: '1r0w5f' }],\n  ['path', { d: 'm21.7 16.5-.9.4', key: '1knoei' }],\n  ['path', { d: 'm15.2 19.1-.9.4', key: 'j188fs' }],\n  ['path', { d: 'm19.5 21.7-.4-.9', key: '1tonu5' }],\n  ['path', { d: 'm16.9 15.2-.4-.9', key: '699xu' }],\n]);\n\nexport default UserRoundCog;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAS,EACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}