[{"C:\\Users\\<USER>\\others\\bdc_app\\src\\index.js": "1", "C:\\Users\\<USER>\\others\\bdc_app\\src\\App.js": "2", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\Dashboard.js": "3", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\Login.js": "4", "C:\\Users\\<USER>\\others\\bdc_app\\src\\contexts\\AuthContext.js": "5", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\DashboardView.js": "6", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\PartnersView.js": "7", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\TransactionsView.js": "8", "C:\\Users\\<USER>\\others\\bdc_app\\src\\services\\api.js": "9", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\modals\\NewTransactionModal.js": "10", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\modals\\BalancingModal.js": "11", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\modals\\NewPartnerModal.js": "12", "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\modals\\PartnerDetailsModal.js": "13"}, {"size": 254, "mtime": 1751291152612, "results": "14", "hashOfConfig": "15"}, {"size": 1146, "mtime": 1751291170251, "results": "16", "hashOfConfig": "15"}, {"size": 8251, "mtime": 1751291279101, "results": "17", "hashOfConfig": "15"}, {"size": 4338, "mtime": 1751291228612, "results": "18", "hashOfConfig": "15"}, {"size": 2177, "mtime": 1751301546020, "results": "19", "hashOfConfig": "15"}, {"size": 8820, "mtime": 1751291346578, "results": "20", "hashOfConfig": "15"}, {"size": 9085, "mtime": 1751301418270, "results": "21", "hashOfConfig": "15"}, {"size": 11857, "mtime": 1751291445372, "results": "22", "hashOfConfig": "15"}, {"size": 4836, "mtime": 1751301596547, "results": "23", "hashOfConfig": "15"}, {"size": 7114, "mtime": 1751291471561, "results": "24", "hashOfConfig": "15"}, {"size": 7204, "mtime": 1751291524331, "results": "25", "hashOfConfig": "15"}, {"size": 5746, "mtime": 1751291495564, "results": "26", "hashOfConfig": "15"}, {"size": 8516, "mtime": 1751291555971, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xevnrn", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\others\\bdc_app\\src\\index.js", [], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\App.js", ["67", "68"], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\Dashboard.js", ["69", "70", "71", "72", "73"], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\Login.js", ["74"], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\DashboardView.js", [], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\PartnersView.js", [], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\TransactionsView.js", ["75"], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\services\\api.js", ["76"], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\modals\\NewTransactionModal.js", [], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\modals\\BalancingModal.js", [], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\modals\\NewPartnerModal.js", ["77", "78", "79"], [], "C:\\Users\\<USER>\\others\\bdc_app\\src\\components\\modals\\PartnerDetailsModal.js", [], [], {"ruleId": "80", "severity": 1, "message": "81", "line": 1, "column": 17, "nodeType": "82", "messageId": "83", "endLine": 1, "endColumn": 25}, {"ruleId": "80", "severity": 1, "message": "84", "line": 1, "column": 27, "nodeType": "82", "messageId": "83", "endLine": 1, "endColumn": 36}, {"ruleId": "80", "severity": 1, "message": "85", "line": 3, "column": 16, "nodeType": "82", "messageId": "83", "endLine": 3, "endColumn": 26}, {"ruleId": "80", "severity": 1, "message": "86", "line": 3, "column": 28, "nodeType": "82", "messageId": "83", "endLine": 3, "endColumn": 38}, {"ruleId": "80", "severity": 1, "message": "87", "line": 3, "column": 40, "nodeType": "82", "messageId": "83", "endLine": 3, "endColumn": 45}, {"ruleId": "80", "severity": 1, "message": "88", "line": 3, "column": 60, "nodeType": "82", "messageId": "83", "endLine": 3, "endColumn": 63}, {"ruleId": "89", "severity": 1, "message": "90", "line": 41, "column": 6, "nodeType": "91", "endLine": 41, "endColumn": 25, "suggestions": "92"}, {"ruleId": "80", "severity": 1, "message": "84", "line": 1, "column": 27, "nodeType": "82", "messageId": "83", "endLine": 1, "endColumn": 36}, {"ruleId": "93", "severity": 1, "message": "94", "line": 37, "column": 7, "nodeType": "95", "messageId": "96", "endLine": 49, "endColumn": 8}, {"ruleId": "97", "severity": 1, "message": "98", "line": 192, "column": 1, "nodeType": "99", "endLine": 192, "endColumn": 33}, {"ruleId": "100", "severity": 1, "message": "101", "line": 26, "column": 33, "nodeType": "102", "messageId": "103", "endLine": 26, "endColumn": 34, "suggestions": "104"}, {"ruleId": "100", "severity": 1, "message": "105", "line": 26, "column": 45, "nodeType": "102", "messageId": "103", "endLine": 26, "endColumn": 46, "suggestions": "106"}, {"ruleId": "100", "severity": 1, "message": "107", "line": 26, "column": 47, "nodeType": "102", "messageId": "103", "endLine": 26, "endColumn": 48, "suggestions": "108"}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'DollarSign' is defined but never used.", "'TrendingUp' is defined but never used.", "'Users' is defined but never used.", "'Eye' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'getCurrentView'. Either include it or remove the dependency array.", "ArrayExpression", ["109"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["110", "111"], "Unnecessary escape character: \\(.", ["112", "113"], "Unnecessary escape character: \\).", ["114", "115"], {"desc": "116", "fix": "117"}, {"messageId": "118", "fix": "119", "desc": "120"}, {"messageId": "121", "fix": "122", "desc": "123"}, {"messageId": "118", "fix": "124", "desc": "120"}, {"messageId": "121", "fix": "125", "desc": "123"}, {"messageId": "118", "fix": "126", "desc": "120"}, {"messageId": "121", "fix": "127", "desc": "123"}, "Update the dependencies array to be: [getCurrentView, location.pathname]", {"range": "128", "text": "129"}, "removeEscape", {"range": "130", "text": "131"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "132", "text": "133"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "134", "text": "131"}, {"range": "135", "text": "133"}, {"range": "136", "text": "131"}, {"range": "137", "text": "133"}, [1714, 1733], "[get<PERSON><PERSON><PERSON><PERSON>iew, location.pathname]", [626, 627], "", [626, 626], "\\", [638, 639], [638, 638], [640, 641], [640, 640]]