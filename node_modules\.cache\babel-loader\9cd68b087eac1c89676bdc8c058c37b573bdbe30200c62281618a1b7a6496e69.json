{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Save = createLucideIcon(\"Save\", [[\"path\", {\n  d: \"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\",\n  key: \"1owoqh\"\n}], [\"polyline\", {\n  points: \"17 21 17 13 7 13 7 21\",\n  key: \"1md35c\"\n}], [\"polyline\", {\n  points: \"7 3 7 8 15 8\",\n  key: \"8nz8an\"\n}]]);\nexport { Save as default };", "map": {"version": 3, "names": ["Save", "createLucideIcon", "d", "key", "points"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjFINWEyIDIgMCAwIDEtMi0yVjVhMiAyIDAgMCAxIDItMmgxMWw1IDV2MTFhMiAyIDAgMCAxLTIgMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTcgMjEgMTcgMTMgNyAxMyA3IDIxIiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjcgMyA3IDggMTUgOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('Save', [\n  ['path', { d: 'M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z', key: '1owoqh' }],\n  ['polyline', { points: '17 21 17 13 7 13 7 21', key: '1md35c' }],\n  ['polyline', { points: '7 3 7 8 15 8', key: '8nz8an' }],\n]);\n\nexport default Save;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,iEAAmE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChG,CAAC,UAAY;EAAEC,MAAA,EAAQ,uBAAyB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,UAAY;EAAEC,MAAA,EAAQ,cAAgB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}