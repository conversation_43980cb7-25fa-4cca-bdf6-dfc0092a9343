{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Key = createLucideIcon(\"Key\", [[\"circle\", {\n  cx: \"7.5\",\n  cy: \"15.5\",\n  r: \"5.5\",\n  key: \"yqb3hr\"\n}], [\"path\", {\n  d: \"m21 2-9.6 9.6\",\n  key: \"1j0ho8\"\n}], [\"path\", {\n  d: \"m15.5 7.5 3 3L22 7l-3-3\",\n  key: \"1rn1fs\"\n}]]);\nexport { Key as default };", "map": {"version": 3, "names": ["Key", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\key.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Key\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI3LjUiIGN5PSIxNS41IiByPSI1LjUiIC8+CiAgPHBhdGggZD0ibTIxIDItOS42IDkuNiIgLz4KICA8cGF0aCBkPSJtMTUuNSA3LjUgMyAzTDIyIDdsLTMtMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/key\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Key = createLucideIcon('Key', [\n  ['circle', { cx: '7.5', cy: '15.5', r: '5.5', key: 'yqb3hr' }],\n  ['path', { d: 'm21 2-9.6 9.6', key: '1j0ho8' }],\n  ['path', { d: 'm15.5 7.5 3 3L22 7l-3-3', key: '1rn1fs' }],\n]);\n\nexport default Key;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CAAC,QAAU;EAAEC,EAAI;EAAOC,EAAI;EAAQC,CAAG;EAAOC,GAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}