{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst GanttChart = createLucideIcon(\"GanttChart\", [[\"path\", {\n  d: \"M8 6h10\",\n  key: \"9lnwnk\"\n}], [\"path\", {\n  d: \"M6 12h9\",\n  key: \"1g9pqf\"\n}], [\"path\", {\n  d: \"M11 18h7\",\n  key: \"c8dzvl\"\n}]]);\nexport { GanttChart as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>hart", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\gantt-chart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name GanttChart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCA2aDEwIiAvPgogIDxwYXRoIGQ9Ik02IDEyaDkiIC8+CiAgPHBhdGggZD0iTTExIDE4aDciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/gantt-chart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GanttChart = createLucideIcon('GanttChart', [\n  ['path', { d: 'M8 6h10', key: '9lnwnk' }],\n  ['path', { d: 'M6 12h9', key: '1g9pqf' }],\n  ['path', { d: 'M11 18h7', key: 'c8dzvl' }],\n]);\n\nexport default GanttChart;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}