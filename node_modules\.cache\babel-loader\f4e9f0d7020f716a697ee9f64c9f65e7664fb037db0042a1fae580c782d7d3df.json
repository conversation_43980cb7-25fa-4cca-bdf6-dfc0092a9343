{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PhoneOutgoing = createLucideIcon(\"PhoneOutgoing\", [[\"polyline\", {\n  points: \"22 8 22 2 16 2\",\n  key: \"1g204g\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"22\",\n  y1: \"8\",\n  y2: \"2\",\n  key: \"1ggias\"\n}], [\"path\", {\n  d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\",\n  key: \"foiqr5\"\n}]]);\nexport { PhoneOutgoing as default };", "map": {"version": 3, "names": ["PhoneOutgoing", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2", "d"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\phone-outgoing.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PhoneOutgoing\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIyMiA4IDIyIDIgMTYgMiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIyMiIgeTE9IjgiIHkyPSIyIiAvPgogIDxwYXRoIGQ9Ik0yMiAxNi45MnYzYTIgMiAwIDAgMS0yLjE4IDIgMTkuNzkgMTkuNzkgMCAwIDEtOC42My0zLjA3IDE5LjUgMTkuNSAwIDAgMS02LTYgMTkuNzkgMTkuNzkgMCAwIDEtMy4wNy04LjY3QTIgMiAwIDAgMSA0LjExIDJoM2EyIDIgMCAwIDEgMiAxLjcyIDEyLjg0IDEyLjg0IDAgMCAwIC43IDIuODEgMiAyIDAgMCAxLS40NSAyLjExTDguMDkgOS45MWExNiAxNiAwIDAgMCA2IDZsMS4yNy0xLjI3YTIgMiAwIDAgMSAyLjExLS40NSAxMi44NCAxMi44NCAwIDAgMCAyLjgxLjdBMiAyIDAgMCAxIDIyIDE2LjkyeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/phone-outgoing\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PhoneOutgoing = createLucideIcon('PhoneOutgoing', [\n  ['polyline', { points: '22 8 22 2 16 2', key: '1g204g' }],\n  ['line', { x1: '16', x2: '22', y1: '8', y2: '2', key: '1ggias' }],\n  [\n    'path',\n    {\n      d: 'M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z',\n      key: 'foiqr5',\n    },\n  ],\n]);\n\nexport default PhoneOutgoing;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CACE,QACA;EACEK,CAAG;EACHL,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}