{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst HopOff = createLucideIcon(\"HopOff\", [[\"path\", {\n  d: \"M17.5 5.5C19 7 20.5 9 21 11c-1.323.265-2.646.39-4.118.226\",\n  key: \"10j95a\"\n}], [\"path\", {\n  d: \"M5.5 17.5C7 19 9 20.5 11 21c.5-2.5.5-5-1-8.5\",\n  key: \"1mqyjd\"\n}], [\"path\", {\n  d: \"M17.5 17.5c-2.5 0-4 0-6-1\",\n  key: \"11elt5\"\n}], [\"path\", {\n  d: \"M20 11.5c1 1.5 2 3.5 2 4.5\",\n  key: \"13ezvz\"\n}], [\"path\", {\n  d: \"M11.5 20c1.5 1 3.5 2 4.5 2 .5-1.5 0-3-.5-4.5\",\n  key: \"1ufrz1\"\n}], [\"path\", {\n  d: \"M22 22c-2 0-3.5-.5-5.5-1.5\",\n  key: \"1n8vbj\"\n}], [\"path\", {\n  d: \"M4.783 4.782C1.073 8.492 1 14.5 5 18c1-1 2-4.5 1.5-6.5 1.5 1 4 1 5.5.5M8.227 2.57C11.578 1.335 15.453 2.089 18 5c-.88.88-3.7 1.761-5.726 1.618\",\n  key: \"1h85u8\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]]);\nexport { HopOff as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\hop-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name HopOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcuNSA1LjVDMTkgNyAyMC41IDkgMjEgMTFjLTEuMzIzLjI2NS0yLjY0Ni4zOS00LjExOC4yMjYiIC8+CiAgPHBhdGggZD0iTTUuNSAxNy41QzcgMTkgOSAyMC41IDExIDIxYy41LTIuNS41LTUtMS04LjUiIC8+CiAgPHBhdGggZD0iTTE3LjUgMTcuNWMtMi41IDAtNCAwLTYtMSIgLz4KICA8cGF0aCBkPSJNMjAgMTEuNWMxIDEuNSAyIDMuNSAyIDQuNSIgLz4KICA8cGF0aCBkPSJNMTEuNSAyMGMxLjUgMSAzLjUgMiA0LjUgMiAuNS0xLjUgMC0zLS41LTQuNSIgLz4KICA8cGF0aCBkPSJNMjIgMjJjLTIgMC0zLjUtLjUtNS41LTEuNSIgLz4KICA8cGF0aCBkPSJNNC43ODMgNC43ODJDMS4wNzMgOC40OTIgMSAxNC41IDUgMThjMS0xIDItNC41IDEuNS02LjUgMS41IDEgNCAxIDUuNS41TTguMjI3IDIuNTdDMTEuNTc4IDEuMzM1IDE1LjQ1MyAyLjA4OSAxOCA1Yy0uODguODgtMy43IDEuNzYxLTUuNzI2IDEuNjE4IiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIyIiB5Mj0iMjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/hop-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst HopOff = createLucideIcon('HopOff', [\n  ['path', { d: 'M17.5 5.5C19 7 20.5 9 21 11c-1.323.265-2.646.39-4.118.226', key: '10j95a' }],\n  ['path', { d: 'M5.5 17.5C7 19 9 20.5 11 21c.5-2.5.5-5-1-8.5', key: '1mqyjd' }],\n  ['path', { d: 'M17.5 17.5c-2.5 0-4 0-6-1', key: '11elt5' }],\n  ['path', { d: 'M20 11.5c1 1.5 2 3.5 2 4.5', key: '13ezvz' }],\n  ['path', { d: 'M11.5 20c1.5 1 3.5 2 4.5 2 .5-1.5 0-3-.5-4.5', key: '1ufrz1' }],\n  ['path', { d: 'M22 22c-2 0-3.5-.5-5.5-1.5', key: '1n8vbj' }],\n  [\n    'path',\n    {\n      d: 'M4.783 4.782C1.073 8.492 1 14.5 5 18c1-1 2-4.5 1.5-6.5 1.5 1 4 1 5.5.5M8.227 2.57C11.578 1.335 15.453 2.089 18 5c-.88.88-3.7 1.761-5.726 1.618',\n      key: '1h85u8',\n    },\n  ],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n]);\n\nexport default HopOff;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2DAA6D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1F,CAAC,MAAQ;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}