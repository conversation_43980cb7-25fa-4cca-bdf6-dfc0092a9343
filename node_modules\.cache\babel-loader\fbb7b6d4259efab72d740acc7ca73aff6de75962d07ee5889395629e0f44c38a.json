{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FileBadge = createLucideIcon(\"FileBadge\", [[\"path\", {\n  d: \"M4 7V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2h-6\",\n  key: \"qtddq0\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"M5 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z\",\n  key: \"u0c8gj\"\n}], [\"path\", {\n  d: \"M7 16.5 8 22l-3-1-3 1 1-5.5\",\n  key: \"5gm2nr\"\n}]]);\nexport { FileBadge as default };", "map": {"version": 3, "names": ["FileBadge", "createLucideIcon", "d", "key", "points"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\file-badge.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileBadge\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCA3VjRhMiAyIDAgMCAxIDItMmg4LjVMMjAgNy41VjIwYTIgMiAwIDAgMS0yIDJoLTYiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPHBhdGggZD0iTTUgMTdhMyAzIDAgMSAwIDAtNiAzIDMgMCAwIDAgMCA2WiIgLz4KICA8cGF0aCBkPSJNNyAxNi41IDggMjJsLTMtMS0zIDEgMS01LjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-badge\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileBadge = createLucideIcon('FileBadge', [\n  ['path', { d: 'M4 7V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2h-6', key: 'qtddq0' }],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['path', { d: 'M5 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z', key: 'u0c8gj' }],\n  ['path', { d: 'M7 16.5 8 22l-3-1-3 1 1-5.5', key: '5gm2nr' }],\n]);\n\nexport default FileBadge;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,qDAAuD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpF,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}