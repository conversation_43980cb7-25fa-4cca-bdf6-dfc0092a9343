{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Subscript = createLucideIcon(\"Subscript\", [[\"path\", {\n  d: \"m4 5 8 8\",\n  key: \"1eunvl\"\n}], [\"path\", {\n  d: \"m12 5-8 8\",\n  key: \"1ah0jp\"\n}], [\"path\", {\n  d: \"M20 19h-4c0-1.5.44-2 1.5-2.5S20 15.33 20 14c0-.47-.17-.93-.48-1.29a2.11 2.11 0 0 0-2.62-.44c-.42.24-.74.62-.9 1.07\",\n  key: \"e8ta8j\"\n}]]);\nexport { Subscript as default };", "map": {"version": 3, "names": ["Subscript", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\subscript.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Subscript\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNCA1IDggOCIgLz4KICA8cGF0aCBkPSJtMTIgNS04IDgiIC8+CiAgPHBhdGggZD0iTTIwIDE5aC00YzAtMS41LjQ0LTIgMS41LTIuNVMyMCAxNS4zMyAyMCAxNGMwLS40Ny0uMTctLjkzLS40OC0xLjI5YTIuMTEgMi4xMSAwIDAgMC0yLjYyLS40NGMtLjQyLjI0LS43NC42Mi0uOSAxLjA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/subscript\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Subscript = createLucideIcon('Subscript', [\n  ['path', { d: 'm4 5 8 8', key: '1eunvl' }],\n  ['path', { d: 'm12 5-8 8', key: '1ah0jp' }],\n  [\n    'path',\n    {\n      d: 'M20 19h-4c0-1.5.44-2 1.5-2.5S20 15.33 20 14c0-.47-.17-.93-.48-1.29a2.11 2.11 0 0 0-2.62-.44c-.42.24-.74.62-.9 1.07',\n      key: 'e8ta8j',\n    },\n  ],\n]);\n\nexport default Subscript;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}