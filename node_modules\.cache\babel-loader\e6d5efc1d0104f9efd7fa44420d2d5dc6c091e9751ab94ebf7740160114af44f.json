{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Tv = createLucideIcon(\"Tv\", [[\"rect\", {\n  width: \"20\",\n  height: \"15\",\n  x: \"2\",\n  y: \"7\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"10ag99\"\n}], [\"polyline\", {\n  points: \"17 2 12 7 7 2\",\n  key: \"11pgbg\"\n}]]);\nexport { Tv as default };", "map": {"version": 3, "names": ["Tv", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "points"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\tv.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Tv\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHg9IjIiIHk9IjciIHJ4PSIyIiByeT0iMiIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNyAyIDEyIDcgNyAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/tv\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tv = createLucideIcon('Tv', [\n  ['rect', { width: '20', height: '15', x: '2', y: '7', rx: '2', ry: '2', key: '10ag99' }],\n  ['polyline', { points: '17 2 12 7 7 2', key: '11pgbg' }],\n]);\n\nexport default Tv;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,EAAA,GAAKC,gBAAA,CAAiB,IAAM,GAChC,CAAC,MAAQ;EAAEC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG;EAAKC,EAAI;EAAKC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,UAAY;EAAEC,MAAA,EAAQ,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}