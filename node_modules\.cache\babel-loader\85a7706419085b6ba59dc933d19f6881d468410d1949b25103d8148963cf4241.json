{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\others\\\\bdc_app\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport function useAuth() {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport function AuthProvider({\n  children\n}) {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      apiService.setToken(token);\n      loadUser();\n    } else {\n      setLoading(false);\n    }\n  }, []);\n  const loadUser = async () => {\n    try {\n      const userData = await apiService.getCurrentUser();\n      setUser(userData);\n    } catch (error) {\n      console.error('Failed to load user:', error);\n      localStorage.removeItem('token');\n      apiService.setToken(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async (username, password) => {\n    try {\n      const response = await apiService.login(username, password);\n      const {\n        access_token\n      } = response;\n      apiService.setToken(access_token);\n      await loadUser();\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Login error:', error);\n      let errorMessage = 'Login failed';\n      if (error.message) {\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        errorMessage = error;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      }\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('token');\n    apiService.setToken(null);\n  };\n  const register = async userData => {\n    try {\n      await apiService.register(userData);\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    register,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n}\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "apiService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "setToken", "loadUser", "userData", "getCurrentUser", "error", "console", "removeItem", "login", "username", "password", "response", "access_token", "success", "errorMessage", "message", "detail", "logout", "register", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/others/bdc_app/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport apiService from '../services/api';\n\nconst AuthContext = createContext();\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\nexport function AuthProvider({ children }) {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      apiService.setToken(token);\n      loadUser();\n    } else {\n      setLoading(false);\n    }\n  }, []);\n\n  const loadUser = async () => {\n    try {\n      const userData = await apiService.getCurrentUser();\n      setUser(userData);\n    } catch (error) {\n      console.error('Failed to load user:', error);\n      localStorage.removeItem('token');\n      apiService.setToken(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (username, password) => {\n    try {\n      const response = await apiService.login(username, password);\n      const { access_token } = response;\n\n      apiService.setToken(access_token);\n      await loadUser();\n\n      return { success: true };\n    } catch (error) {\n      console.error('Login error:', error);\n      let errorMessage = 'Login failed';\n\n      if (error.message) {\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        errorMessage = error;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      }\n\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('token');\n    apiService.setToken(null);\n  };\n\n  const register = async (userData) => {\n    try {\n      await apiService.register(userData);\n      return { success: true };\n    } catch (error) {\n      return { success: false, error: error.message };\n    }\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    register,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,SAASQ,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB;AAACD,EAAA,CANeD,OAAO;AAQvB,OAAO,SAASI,YAAYA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,GAAA;EACzC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTf,UAAU,CAACkB,QAAQ,CAACH,KAAK,CAAC;MAC1BI,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLL,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,UAAU,CAACqB,cAAc,CAAC,CAAC;MAClDT,OAAO,CAACQ,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CN,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;MAChCxB,UAAU,CAACkB,QAAQ,CAAC,IAAI,CAAC;IAC3B,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM5B,UAAU,CAACyB,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC;MAC3D,MAAM;QAAEE;MAAa,CAAC,GAAGD,QAAQ;MAEjC5B,UAAU,CAACkB,QAAQ,CAACW,YAAY,CAAC;MACjC,MAAMV,QAAQ,CAAC,CAAC;MAEhB,OAAO;QAAEW,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIS,YAAY,GAAG,cAAc;MAEjC,IAAIT,KAAK,CAACU,OAAO,EAAE;QACjBD,YAAY,GAAGT,KAAK,CAACU,OAAO;MAC9B,CAAC,MAAM,IAAI,OAAOV,KAAK,KAAK,QAAQ,EAAE;QACpCS,YAAY,GAAGT,KAAK;MACtB,CAAC,MAAM,IAAIA,KAAK,CAACW,MAAM,EAAE;QACvBF,YAAY,GAAGT,KAAK,CAACW,MAAM;MAC7B;MAEA,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAER,KAAK,EAAES;MAAa,CAAC;IAChD;EACF,CAAC;EAED,MAAMG,MAAM,GAAGA,CAAA,KAAM;IACnBtB,OAAO,CAAC,IAAI,CAAC;IACbI,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;IAChCxB,UAAU,CAACkB,QAAQ,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMiB,QAAQ,GAAG,MAAOf,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMpB,UAAU,CAACmC,QAAQ,CAACf,QAAQ,CAAC;MACnC,OAAO;QAAEU,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACd,OAAO;QAAEQ,OAAO,EAAE,KAAK;QAAER,KAAK,EAAEA,KAAK,CAACU;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMI,KAAK,GAAG;IACZzB,IAAI;IACJc,KAAK;IACLS,MAAM;IACNC,QAAQ;IACRtB;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,WAAW,CAACkC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3B,QAAA,EAChCA;EAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B;AAAC/B,GAAA,CAhFeF,YAAY;AAAAkC,EAAA,GAAZlC,YAAY;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}