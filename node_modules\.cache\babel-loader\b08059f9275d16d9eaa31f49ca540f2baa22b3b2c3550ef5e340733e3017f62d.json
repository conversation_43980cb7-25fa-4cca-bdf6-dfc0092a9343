{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ArrowUpZA = createLucideIcon(\"ArrowUpZA\", [[\"path\", {\n  d: \"m3 8 4-4 4 4\",\n  key: \"11wl7u\"\n}], [\"path\", {\n  d: \"M7 4v16\",\n  key: \"1glfcx\"\n}], [\"path\", {\n  d: \"M15 4h5l-5 6h5\",\n  key: \"8asdl1\"\n}], [\"path\", {\n  d: \"M15 20v-3.5a2.5 2.5 0 0 1 5 0V20\",\n  key: \"r6l5cz\"\n}], [\"path\", {\n  d: \"M20 18h-5\",\n  key: \"18j1r2\"\n}]]);\nexport { ArrowUpZA as default };", "map": {"version": 3, "names": ["ArrowUpZA", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\arrow-up-z-a.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowUpZA\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyA4IDQtNCA0IDQiIC8+CiAgPHBhdGggZD0iTTcgNHYxNiIgLz4KICA8cGF0aCBkPSJNMTUgNGg1bC01IDZoNSIgLz4KICA8cGF0aCBkPSJNMTUgMjB2LTMuNWEyLjUgMi41IDAgMCAxIDUgMFYyMCIgLz4KICA8cGF0aCBkPSJNMjAgMThoLTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-up-z-a\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowUpZA = createLucideIcon('ArrowUpZA', [\n  ['path', { d: 'm3 8 4-4 4 4', key: '11wl7u' }],\n  ['path', { d: 'M7 4v16', key: '1glfcx' }],\n  ['path', { d: 'M15 4h5l-5 6h5', key: '8asdl1' }],\n  ['path', { d: 'M15 20v-3.5a2.5 2.5 0 0 1 5 0V20', key: 'r6l5cz' }],\n  ['path', { d: 'M20 18h-5', key: '18j1r2' }],\n]);\n\nexport default ArrowUpZA;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}