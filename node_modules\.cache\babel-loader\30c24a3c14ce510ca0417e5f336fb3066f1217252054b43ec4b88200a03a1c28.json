{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst MoveVertical = createLucideIcon(\"MoveVertical\", [[\"polyline\", {\n  points: \"8 18 12 22 16 18\",\n  key: \"1uutw3\"\n}], [\"polyline\", {\n  points: \"8 6 12 2 16 6\",\n  key: \"d60sxy\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"7eqyqh\"\n}]]);\nexport { MoveVertical as default };", "map": {"version": 3, "names": ["MoveVertical", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\move-vertical.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MoveVertical\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI4IDE4IDEyIDIyIDE2IDE4IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjggNiAxMiAyIDE2IDYiIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMTIiIHkxPSIyIiB5Mj0iMjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/move-vertical\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MoveVertical = createLucideIcon('MoveVertical', [\n  ['polyline', { points: '8 18 12 22 16 18', key: '1uutw3' }],\n  ['polyline', { points: '8 6 12 2 16 6', key: 'd60sxy' }],\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n]);\n\nexport default MoveVertical;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,UAAY;EAAEC,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,UAAY;EAAED,MAAA,EAAQ,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}