const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

class ApiService {
  constructor() {
    this.token = localStorage.getItem('token');
  }

  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('token', token);
    } else {
      localStorage.removeItem('token');
    }
  }

  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    if (config.body && typeof config.body !== 'string') {
      config.body = JSON.stringify(config.body);
    }

    try {
      const response = await fetch(url, config);
      
      if (response.status === 401) {
        this.setToken(null);
        window.location.href = '/login';
        return;
      }
      
      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.detail) {
            errorMessage = errorData.detail;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          // If we can't parse the error response, use the status message
          errorMessage = `Server error: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Auth endpoints
  async login(username, password) {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);

    // For OAuth2PasswordRequestForm, we need to send form data without JSON content-type
    const url = `${API_BASE_URL}/auth/token`;
    const config = {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header - let browser set it for FormData
    };

    if (this.token) {
      config.headers = { Authorization: `Bearer ${this.token}` };
    }

    try {
      const response = await fetch(url, config);

      if (response.status === 401) {
        this.setToken(null);
        window.location.href = '/login';
        return;
      }

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.detail) {
            errorMessage = errorData.detail;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          errorMessage = `Server error: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      return await response.json();
    } catch (error) {
      console.error('Login API request failed:', error);
      throw error;
    }
  }

  async getCurrentUser() {
    return this.request('/auth/me');
  }

  async register(userData) {
    return this.request('/auth/register', {
      method: 'POST',
      body: userData,
    });
  }

  // Partner endpoints
  async getPartners() {
    return this.request('/partners');
  }

  async createPartner(partnerData) {
    return this.request('/partners', {
      method: 'POST',
      body: partnerData,
    });
  }

  async updatePartner(partnerId, partnerData) {
    return this.request(`/partners/${partnerId}`, {
      method: 'PUT',
      body: partnerData,
    });
  }

  async getPartner(partnerId) {
    return this.request(`/partners/${partnerId}`);
  }

  async getPartnerBalance(partnerId) {
    return this.request(`/partners/${partnerId}/balance`);
  }

  // Transaction endpoints
  async getTransactions(partnerId = null) {
    const params = partnerId ? `?partner_id=${partnerId}` : '';
    return this.request(`/transactions${params}`);
  }

  async createTransaction(transactionData) {
    return this.request('/transactions', {
      method: 'POST',
      body: transactionData,
    });
  }

  async updateTransaction(transactionId, transactionData) {
    return this.request(`/transactions/${transactionId}`, {
      method: 'PUT',
      body: transactionData,
    });
  }

  async processBalance(balanceData) {
    return this.request('/transactions/balance', {
      method: 'POST',
      body: balanceData,
    });
  }

  // Reports endpoints
  async getDashboardStats() {
    return this.request('/reports/dashboard');
  }

  async getDailyReport(date = null) {
    const params = date ? `?date=${date}` : '';
    return this.request(`/reports/daily${params}`);
  }
}

export default new ApiService();
