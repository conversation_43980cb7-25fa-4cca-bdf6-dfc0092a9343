{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\others\\\\bdc_app\\\\src\\\\components\\\\PartnersView.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Plus, Eye, DollarSign, Phone, Mail, Calendar, Users } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PartnersView = ({\n  partners,\n  onNewPartner,\n  onViewPartner,\n  onBalance\n}) => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const formatCurrency = amount => {\n    return `$${amount.toLocaleString()}`;\n  };\n  const filteredPartners = partners.filter(partner => {\n    var _partner$phone, _partner$email;\n    const matchesSearch = partner.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_partner$phone = partner.phone) === null || _partner$phone === void 0 ? void 0 : _partner$phone.toLowerCase().includes(searchTerm.toLowerCase())) || ((_partner$email = partner.email) === null || _partner$email === void 0 ? void 0 : _partner$email.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesStatus = statusFilter === 'all' || partner.status === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'bg-green-100 text-green-800';\n      case 'inactive':\n        return 'bg-gray-100 text-gray-800';\n      case 'suspended':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Trading Partners\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage your trading partners and their accounts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onNewPartner,\n        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Add Partner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search partners...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: statusFilter,\n            onChange: e => setStatusFilter(e.target.value),\n            className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"active\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"inactive\",\n              children: \"Inactive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"suspended\",\n              children: \"Suspended\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: filteredPartners.map(partner => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow hover:shadow-md transition-shadow\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-start mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: partner.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(partner.status)}`,\n                children: partner.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onViewPartner(partner),\n              className: \"p-2 text-gray-400 hover:text-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(Eye, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 mb-4\",\n            children: [partner.phone && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(Phone, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 21\n              }, this), partner.phone]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 19\n            }, this), partner.email && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 21\n              }, this), partner.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this), \"Joined \", new Date(partner.created_at).toLocaleDateString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-t pt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Current Balance:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-lg\",\n                children: formatCurrency(partner.current_balance || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Total Given:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: formatCurrency(partner.total_given || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Total Returned:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: formatCurrency(partner.total_returned || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onViewPartner(partner),\n                className: \"flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700\",\n                children: \"View Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), partner.current_balance > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onBalance(partner),\n                className: \"flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700 flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                  className: \"h-4 w-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 23\n                }, this), \"Balance\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)\n      }, partner.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), filteredPartners.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mx-auto h-12 w-12 text-gray-400\",\n        children: /*#__PURE__*/_jsxDEV(Users, {\n          className: \"h-12 w-12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mt-2 text-sm font-medium text-gray-900\",\n        children: \"No partners found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: searchTerm || statusFilter !== 'all' ? 'Try adjusting your search or filter criteria.' : 'Get started by adding your first trading partner.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this), !searchTerm && statusFilter === 'all' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onNewPartner,\n          className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add First Partner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Partners Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-blue-600\",\n            children: partners.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Total Partners\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-green-600\",\n            children: partners.filter(p => p.status === 'active').length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-yellow-600\",\n            children: partners.filter(p => p.current_balance > 0).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"With Balance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-purple-600\",\n            children: formatCurrency(partners.reduce((sum, p) => sum + (p.current_balance || 0), 0))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Total Outstanding\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(PartnersView, \"wbBPZ9F0sZsvL7kUkncOVmp94Fc=\");\n_c = PartnersView;\nexport default PartnersView;\nvar _c;\n$RefreshReg$(_c, \"PartnersView\");", "map": {"version": 3, "names": ["React", "useState", "Plus", "Eye", "DollarSign", "Phone", "Mail", "Calendar", "Users", "jsxDEV", "_jsxDEV", "PartnersView", "partners", "onNew<PERSON><PERSON><PERSON>", "onViewP<PERSON>ner", "onBalance", "_s", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "formatCurrency", "amount", "toLocaleString", "filteredPartners", "filter", "partner", "_partner$phone", "_partner$email", "matchesSearch", "name", "toLowerCase", "includes", "phone", "email", "matchesStatus", "status", "getStatusColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "map", "Date", "created_at", "toLocaleDateString", "current_balance", "total_given", "total_returned", "id", "length", "p", "reduce", "sum", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/others/bdc_app/src/components/PartnersView.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Plus, Eye, DollarSign, Phone, Mail, Calendar, Users } from 'lucide-react';\n\nconst PartnersView = ({ partners, onNewPartner, onViewPartner, onBalance }) => {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n\n  const formatCurrency = (amount) => {\n    return `$${amount.toLocaleString()}`;\n  };\n\n  const filteredPartners = partners.filter(partner => {\n    const matchesSearch = partner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         partner.phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         partner.email?.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesStatus = statusFilter === 'all' || partner.status === statusFilter;\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active':\n        return 'bg-green-100 text-green-800';\n      case 'inactive':\n        return 'bg-gray-100 text-gray-800';\n      case 'suspended':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">Trading Partners</h2>\n          <p className=\"text-gray-600\">Manage your trading partners and their accounts</p>\n        </div>\n        <button\n          onClick={onNewPartner}\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\n        >\n          <Plus className=\"h-4 w-4\" />\n          <span>Add Partner</span>\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white p-4 rounded-lg shadow\">\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <input\n              type=\"text\"\n              placeholder=\"Search partners...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n          <div>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"active\">Active</option>\n              <option value=\"inactive\">Inactive</option>\n              <option value=\"suspended\">Suspended</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Partners Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredPartners.map((partner) => (\n          <div key={partner.id} className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow\">\n            <div className=\"p-6\">\n              {/* Partner Header */}\n              <div className=\"flex justify-between items-start mb-4\">\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900\">{partner.name}</h3>\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(partner.status)}`}>\n                    {partner.status}\n                  </span>\n                </div>\n                <button\n                  onClick={() => onViewPartner(partner)}\n                  className=\"p-2 text-gray-400 hover:text-gray-600\"\n                >\n                  <Eye className=\"h-4 w-4\" />\n                </button>\n              </div>\n\n              {/* Contact Info */}\n              <div className=\"space-y-2 mb-4\">\n                {partner.phone && (\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <Phone className=\"h-4 w-4 mr-2\" />\n                    {partner.phone}\n                  </div>\n                )}\n                {partner.email && (\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <Mail className=\"h-4 w-4 mr-2\" />\n                    {partner.email}\n                  </div>\n                )}\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Calendar className=\"h-4 w-4 mr-2\" />\n                  Joined {new Date(partner.created_at).toLocaleDateString()}\n                </div>\n              </div>\n\n              {/* Balance Info */}\n              <div className=\"border-t pt-4\">\n                <div className=\"flex justify-between items-center mb-2\">\n                  <span className=\"text-sm text-gray-600\">Current Balance:</span>\n                  <span className=\"font-semibold text-lg\">\n                    {formatCurrency(partner.current_balance || 0)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center mb-2\">\n                  <span className=\"text-sm text-gray-600\">Total Given:</span>\n                  <span className=\"text-sm\">\n                    {formatCurrency(partner.total_given || 0)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center mb-4\">\n                  <span className=\"text-sm text-gray-600\">Total Returned:</span>\n                  <span className=\"text-sm\">\n                    {formatCurrency(partner.total_returned || 0)}\n                  </span>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={() => onViewPartner(partner)}\n                    className=\"flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700\"\n                  >\n                    View Details\n                  </button>\n                  {partner.current_balance > 0 && (\n                    <button\n                      onClick={() => onBalance(partner)}\n                      className=\"flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700 flex items-center justify-center\"\n                    >\n                      <DollarSign className=\"h-4 w-4 mr-1\" />\n                      Balance\n                    </button>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Empty State */}\n      {filteredPartners.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"mx-auto h-12 w-12 text-gray-400\">\n            <Users className=\"h-12 w-12\" />\n          </div>\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No partners found</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            {searchTerm || statusFilter !== 'all' \n              ? 'Try adjusting your search or filter criteria.'\n              : 'Get started by adding your first trading partner.'\n            }\n          </p>\n          {!searchTerm && statusFilter === 'all' && (\n            <div className=\"mt-6\">\n              <button\n                onClick={onNewPartner}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 mx-auto\"\n              >\n                <Plus className=\"h-4 w-4\" />\n                <span>Add First Partner</span>\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Summary Stats */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Partners Summary</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">{partners.length}</div>\n            <div className=\"text-sm text-gray-600\">Total Partners</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-green-600\">\n              {partners.filter(p => p.status === 'active').length}\n            </div>\n            <div className=\"text-sm text-gray-600\">Active</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-yellow-600\">\n              {partners.filter(p => p.current_balance > 0).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">With Balance</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-purple-600\">\n              {formatCurrency(partners.reduce((sum, p) => sum + (p.current_balance || 0), 0))}\n            </div>\n            <div className=\"text-sm text-gray-600\">Total Outstanding</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PartnersView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnF,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY;EAAEC,aAAa;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMoB,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIA,MAAM,CAACC,cAAc,CAAC,CAAC,EAAE;EACtC,CAAC;EAED,MAAMC,gBAAgB,GAAGZ,QAAQ,CAACa,MAAM,CAACC,OAAO,IAAI;IAAA,IAAAC,cAAA,EAAAC,cAAA;IAClD,MAAMC,aAAa,GAAGH,OAAO,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACf,UAAU,CAACc,WAAW,CAAC,CAAC,CAAC,MAAAJ,cAAA,GAC9DD,OAAO,CAACO,KAAK,cAAAN,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACf,UAAU,CAACc,WAAW,CAAC,CAAC,CAAC,OAAAH,cAAA,GAC/DF,OAAO,CAACQ,KAAK,cAAAN,cAAA,uBAAbA,cAAA,CAAeG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACf,UAAU,CAACc,WAAW,CAAC,CAAC,CAAC;IAEpF,MAAMI,aAAa,GAAGhB,YAAY,KAAK,KAAK,IAAIO,OAAO,CAACU,MAAM,KAAKjB,YAAY;IAE/E,OAAOU,aAAa,IAAIM,aAAa;EACvC,CAAC,CAAC;EAEF,MAAME,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,6BAA6B;MACtC,KAAK,UAAU;QACb,OAAO,2BAA2B;MACpC,KAAK,WAAW;QACd,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,oBACE1B,OAAA;IAAK4B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB7B,OAAA;MAAK4B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD7B,OAAA;QAAA6B,QAAA,gBACE7B,OAAA;UAAI4B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEjC,OAAA;UAAG4B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,eACNjC,OAAA;QACEkC,OAAO,EAAE/B,YAAa;QACtByB,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBAErG7B,OAAA,CAACR,IAAI;UAACoC,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BjC,OAAA;UAAA6B,QAAA,EAAM;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C7B,OAAA;QAAK4B,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C7B,OAAA;UAAK4B,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrB7B,OAAA;YACEmC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAE9B,UAAW;YAClB+B,QAAQ,EAAGC,CAAC,IAAK/B,aAAa,CAAC+B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAAwG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjC,OAAA;UAAA6B,QAAA,eACE7B,OAAA;YACEqC,KAAK,EAAE5B,YAAa;YACpB6B,QAAQ,EAAGC,CAAC,IAAK7B,eAAe,CAAC6B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACjDT,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAE3G7B,OAAA;cAAQqC,KAAK,EAAC,KAAK;cAAAR,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvCjC,OAAA;cAAQqC,KAAK,EAAC,QAAQ;cAAAR,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCjC,OAAA;cAAQqC,KAAK,EAAC,UAAU;cAAAR,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1CjC,OAAA;cAAQqC,KAAK,EAAC,WAAW;cAAAR,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEf,gBAAgB,CAAC2B,GAAG,CAAEzB,OAAO,iBAC5BhB,OAAA;QAAsB4B,SAAS,EAAC,8DAA8D;QAAAC,QAAA,eAC5F7B,OAAA;UAAK4B,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAElB7B,OAAA;YAAK4B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD7B,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAI4B,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAEb,OAAO,CAACI;cAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvEjC,OAAA;gBAAM4B,SAAS,EAAE,4DAA4DD,cAAc,CAACX,OAAO,CAACU,MAAM,CAAC,EAAG;gBAAAG,QAAA,EAC3Gb,OAAO,CAACU;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjC,OAAA;cACEkC,OAAO,EAAEA,CAAA,KAAM9B,aAAa,CAACY,OAAO,CAAE;cACtCY,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eAEjD7B,OAAA,CAACP,GAAG;gBAACmC,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAC5Bb,OAAO,CAACO,KAAK,iBACZvB,OAAA;cAAK4B,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACtD7B,OAAA,CAACL,KAAK;gBAACiC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACjCjB,OAAO,CAACO,KAAK;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACN,EACAjB,OAAO,CAACQ,KAAK,iBACZxB,OAAA;cAAK4B,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACtD7B,OAAA,CAACJ,IAAI;gBAACgC,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAChCjB,OAAO,CAACQ,KAAK;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACN,eACDjC,OAAA;cAAK4B,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACtD7B,OAAA,CAACH,QAAQ;gBAAC+B,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAC9B,EAAC,IAAIS,IAAI,CAAC1B,OAAO,CAAC2B,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjC,OAAA;YAAK4B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7B,OAAA;cAAK4B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD7B,OAAA;gBAAM4B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/DjC,OAAA;gBAAM4B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpClB,cAAc,CAACK,OAAO,CAAC6B,eAAe,IAAI,CAAC;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjC,OAAA;cAAK4B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD7B,OAAA;gBAAM4B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DjC,OAAA;gBAAM4B,SAAS,EAAC,SAAS;gBAAAC,QAAA,EACtBlB,cAAc,CAACK,OAAO,CAAC8B,WAAW,IAAI,CAAC;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjC,OAAA;cAAK4B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD7B,OAAA;gBAAM4B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9DjC,OAAA;gBAAM4B,SAAS,EAAC,SAAS;gBAAAC,QAAA,EACtBlB,cAAc,CAACK,OAAO,CAAC+B,cAAc,IAAI,CAAC;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNjC,OAAA;cAAK4B,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7B,OAAA;gBACEkC,OAAO,EAAEA,CAAA,KAAM9B,aAAa,CAACY,OAAO,CAAE;gBACtCY,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,EACtF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACRjB,OAAO,CAAC6B,eAAe,GAAG,CAAC,iBAC1B7C,OAAA;gBACEkC,OAAO,EAAEA,CAAA,KAAM7B,SAAS,CAACW,OAAO,CAAE;gBAClCY,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,gBAExH7B,OAAA,CAACN,UAAU;kBAACkC,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA9EEjB,OAAO,CAACgC,EAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+Ef,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLnB,gBAAgB,CAACmC,MAAM,KAAK,CAAC,iBAC5BjD,OAAA;MAAK4B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC7B,OAAA;QAAK4B,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9C7B,OAAA,CAACF,KAAK;UAAC8B,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACNjC,OAAA;QAAI4B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7EjC,OAAA;QAAG4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACtCtB,UAAU,IAAIE,YAAY,KAAK,KAAK,GACjC,+CAA+C,GAC/C;MAAmD;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEtD,CAAC,EACH,CAAC1B,UAAU,IAAIE,YAAY,KAAK,KAAK,iBACpCT,OAAA;QAAK4B,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB7B,OAAA;UACEkC,OAAO,EAAE/B,YAAa;UACtByB,SAAS,EAAC,mGAAmG;UAAAC,QAAA,gBAE7G7B,OAAA,CAACR,IAAI;YAACoC,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BjC,OAAA;YAAA6B,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDjC,OAAA;MAAK4B,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7C7B,OAAA;QAAI4B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5EjC,OAAA;QAAK4B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD7B,OAAA;UAAK4B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7B,OAAA;YAAK4B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE3B,QAAQ,CAAC+C;UAAM;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEjC,OAAA;YAAK4B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7B,OAAA;YAAK4B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/C3B,QAAQ,CAACa,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAACxB,MAAM,KAAK,QAAQ,CAAC,CAACuB;UAAM;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7B,OAAA;YAAK4B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAChD3B,QAAQ,CAACa,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAACL,eAAe,GAAG,CAAC,CAAC,CAACI;UAAM;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7B,OAAA;YAAK4B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAChDlB,cAAc,CAACT,QAAQ,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,IAAIF,CAAC,CAACL,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA1NIL,YAAY;AAAAoD,EAAA,GAAZpD,YAAY;AA4NlB,eAAeA,YAAY;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}