{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Forward = createLucideIcon(\"Forward\", [[\"polyline\", {\n  points: \"15 17 20 12 15 7\",\n  key: \"1w3sku\"\n}], [\"path\", {\n  d: \"M4 18v-2a4 4 0 0 1 4-4h12\",\n  key: \"jmiej9\"\n}]]);\nexport { Forward as default };", "map": {"version": 3, "names": ["Forward", "createLucideIcon", "points", "key", "d"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\forward.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Forward\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNSAxNyAyMCAxMiAxNSA3IiAvPgogIDxwYXRoIGQ9Ik00IDE4di0yYTQgNCAwIDAgMSA0LTRoMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/forward\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Forward = createLucideIcon('Forward', [\n  ['polyline', { points: '15 17 20 12 15 7', key: '1w3sku' }],\n  ['path', { d: 'M4 18v-2a4 4 0 0 1 4-4h12', key: 'jmiej9' }],\n]);\n\nexport default Forward;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,UAAY;EAAEC,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}