{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Orbit = createLucideIcon(\"Orbit\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"mhkx31\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"v8kfzx\"\n}], [\"path\", {\n  d: \"M10.4 21.9a10 10 0 0 0 9.941-15.416\",\n  key: \"eohfx2\"\n}], [\"path\", {\n  d: \"M13.5 2.1a10 10 0 0 0-9.841 15.416\",\n  key: \"19pvbm\"\n}]]);\nexport { Orbit as default };", "map": {"version": 3, "names": ["Orbit", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\others\\bdc_app\\node_modules\\lucide-react\\src\\icons\\orbit.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Orbit\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iNSIgcj0iMiIgLz4KICA8Y2lyY2xlIGN4PSI1IiBjeT0iMTkiIHI9IjIiIC8+CiAgPHBhdGggZD0iTTEwLjQgMjEuOWExMCAxMCAwIDAgMCA5Ljk0MS0xNS40MTYiIC8+CiAgPHBhdGggZD0iTTEzLjUgMi4xYTEwIDEwIDAgMCAwLTkuODQxIDE1LjQxNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/orbit\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Orbit = createLucideIcon('Orbit', [\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n  ['circle', { cx: '19', cy: '5', r: '2', key: 'mhkx31' }],\n  ['circle', { cx: '5', cy: '19', r: '2', key: 'v8kfzx' }],\n  ['path', { d: 'M10.4 21.9a10 10 0 0 0 9.941-15.416', key: 'eohfx2' }],\n  ['path', { d: 'M13.5 2.1a10 10 0 0 0-9.841 15.416', key: '19pvbm' }],\n]);\n\nexport default Orbit;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEH,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,qCAAuC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAEC,CAAA,EAAG,oCAAsC;EAAAD,GAAA,EAAK;AAAA,CAAU,EACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}